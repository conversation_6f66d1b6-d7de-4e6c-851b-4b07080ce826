<div class="mycoolinput">
  <input {% if pattern and pattern != '' %}pattern="{{ pattern }}"{% endif %} placeholder=" " type="{{ type | default('text') }}" {% if value %}value="{{ value }}"{% endif %} name="{{ name | default(namealwayschange) }}" id="{{ namealwayschange }}" {% if required is not defined or required %} required{% endif %} />
  <label class="mycoolinputslabel">
    {{ label | safe }}
  </label>
  <span class="input-group__error">{{ errormessage }}</span>
</div>
