{"data_mtime": 1759241332, "dep_lines": [5, 2, 8, 9, 10, 11, 13, 14, 15, 944, 1, 3, 4, 6, 7, 946, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["static.pdf.fontloader", "fastapi.responses", "app.db", "app.users", "app.config", "jinja2_fragments.fastapi", "datastar_py.fastapi", "datastar_py.sse", "app.user_settings_db", "app.mail_utils", "<PERSON><PERSON><PERSON>", "typing", "math", "fpdf", "datetime", "io", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "contextlib", "datastar_py", "datastar_py.starlette", "enum", "fastapi.datastructures", "fastapi.param_functions", "fastapi.params", "fastapi.routing", "fastapi_users_db_sqlalchemy", "fpdf.enums", "fpdf.fpdf", "fpdf.graphics_state", "fpdf.output", "fpdf.transitions", "jinja2", "jinja2.environment", "jinja2.runtime", "jinja2_fragments", "sqlalchemy", "sqlalchemy.inspection", "sqlalchemy.orm", "sqlalchemy.orm.decl_api", "starlette", "starlette.background", "starlette.datastructures", "starlette.requests", "starlette.responses", "starlette.routing", "starlette.templating", "types"], "hash": "5386800cf06508f63949ce60af12f1201e8686a0", "id": "app.calc01", "ignore_all": false, "interface_hash": "56d328cce127513fe3dcb3fa890e65da86b9023e", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\calc01.py", "plugin_data": null, "size": 32197, "suppressed": [], "version_id": "1.15.0"}