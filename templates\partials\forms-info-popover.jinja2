<div class="info-popover-container">
  <style>
      me {
          position: relative;
          display: inline-block;
      }

      me .info-button {
          all: unset;
          margin-bottom: calc({{ marginbottom }} / 16 * 1rem);
          font-family: 'Noto Serif', serif;
          font-size: 1.125rem;
          font-weight: 500;
          background-color: transparent;
          border: 1px solid var(--color-input-lines);
          border-radius: 0.5rem;
          color: var(--color-input-lines);
          width: 1.5625rem;
          height: 1.5625rem;
          margin-left: 0.5rem;
          text-align: center;
          cursor: pointer;
          transition: background-color 0.3s ease, color 0.3s ease;
          display: inline-block;
      }

      me .info-button:hover {
          background-color: var(--color-background-dark);
          color: var(--color-text-brighter);
      }

      .info-content-global {
          position: fixed !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;
          background-color: var(--color-text-dark);
          border: 1px solid var(--color-background-dark);
          border-radius: 0.875rem;
          padding: 0.25rem;
          z-index: 1000 !important;
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.3s ease, visibility 0.3s ease;
          display: block !important;
      }

      .info-content-global.show {
          opacity: 1 !important;
          visibility: visible !important;
      }
  </style>

  <button type="button" class="info-button" id="info_btn_{{ namealwayschange }}" tabindex="0">i</button>
</div>

<div id="info_content_{{ namealwayschange }}" class="info-content-global">
  {{ infohtml | safe }}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const content = document.getElementById('info_content_{{ namealwayschange }}');
  if (content) {
    document.body.appendChild(content);
  }
});
</script>
