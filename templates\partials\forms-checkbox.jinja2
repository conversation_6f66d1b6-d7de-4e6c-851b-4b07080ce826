<div>
  <style>
    me {
      position: relative;
      height: 5.9375rem;
      display: flex;
      flex-basis: 100%;
      width: 100%;
      max-width: 100%;
      overflow: hidden;
    }
  </style>
  <div>
    <style>
      me {
        display: flex;
        flex-basis: 100%;
        align-items: center;
        justify-content: space-between;
        height: 2rem;
        top: 1.875rem;
        position: relative;
        width: 100%;
        max-width: 100%;
        border-bottom: 1px solid var(--color-input-lines);
      }
      me *,
      me ::after,
      me ::before {
        box-sizing: border-box;
      }
      me .label-text {
        font-family: 'Noto Sans', sans-serif;
        font-size: 1.125rem;
        color: var(--color-text-black);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        min-width: 0;
        margin-right: 0.5rem;
      }
      me .switch {
        width: 2.5rem;
        height: 1.25rem;
        position: relative;
        display: inline-block;
        margin-right: 0.3rem;
        padding-left: 1rem;
        flex-shrink: 0;
      }
      me input[type="checkbox"] {
        visibility: hidden;
        opacity: 0;
        position: absolute;

      }
      me .slider {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 1rem;
        cursor: pointer;
        border: 1px solid var(--color-text-black);
        overflow: hidden;
        transition: 0.2s;
      }
      me .slider:before {
        position: absolute;
        content: "";
        width: 1rem;
        height: 1rem;
        left: 0.125rem;
        top: 0.094rem;
        background-color: #764f4f;
        border-radius: 50%;
        transition: 0.2s;
        transform: translateX(0);
      }
      me input:checked + .slider:before {
        transform: translateX(1.1875rem);
        background-color: rgb(32, 128, 32);
      }
      me input:checked + .slider {
        box-shadow: 0 0 0 1px limeGreen;
      }
    </style>
    <span class="label-text">{{ label | safe }}</span>
    <label class="switch">
      <input type="checkbox" value="TRUE" name="{{ name | default(namealwayschange) }}" id="{{ namealwayschange }}">
      <span class="slider"></span>
    </label>
  </div>
</div>




